from django import forms
from django.core.validators import MinLengthValidator


class HelloWorldForm(forms.Form):
    """Simple hello world form for demonstration"""
    name = forms.CharField(max_length=100)


class InventoryItemForm(forms.Form):
    """Basic inventory item form"""
    item_name = forms.CharField(
        max_length=200,
        validators=[MinLengthValidator(3)],
        widget=forms.TextInput(attrs={
            'placeholder': 'Item name',
            'class': 'form-control'
        }),
        help_text='Name of the inventory item'
    )
    
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'placeholder': 'Item description',
            'class': 'form-control'
        }),
        required=False,
        help_text='Detailed description of the item'
    )
    
    quantity = forms.IntegerField(
        min_value=0,
        initial=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '0'
        }),
        help_text='Quantity in stock'
    )
    
    price = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'min': '0'
        }),
        help_text='Price per unit (optional)'
    )
    
    CATEGORY_CHOICES = [
        ('', 'Select Category'),
        ('electronics', 'Electronics'),
        ('furniture', 'Furniture'),
        ('supplies', 'Office Supplies'),
        ('equipment', 'Equipment'),
        ('other', 'Other'),
    ]
    
    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        help_text='Item category'
    )
    
    is_active = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Is this item currently active?'
    )


class SearchForm(forms.Form):
    """Search form for inventory items"""
    query = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'Search inventory...',
            'class': 'form-control'
        })
    )
    
    category = forms.ChoiceField(
        choices=[('', 'All Categories')] + InventoryItemForm.CATEGORY_CHOICES[1:],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
