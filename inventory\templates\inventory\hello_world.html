{% extends 'inventory/base.html' %}

{% block title %}Hello World - System Inventory{% endblock %}

{% block header %}Hello World Page{% endblock %}

{% block content %}
<div class="hello-world-section">
    <h2>{{ message }}</h2>
    
    <div class="info-box">
        <h3>System Information:</h3>
        <ul>
            <li><strong>Project:</strong> System Inventory Management</li>
            <li><strong>Module:</strong> Inventory</li>
            <li><strong>Framework:</strong> Django</li>
            <li><strong>Status:</strong> Active and Running</li>
        </ul>
    </div>
    
    <div class="demo-content">
        <h3>Demo Content:</h3>
        <p>This is a basic hello world template demonstrating the Django template system.</p>
        <p>Current timestamp: <span id="timestamp"></span></p>
        
        <div class="sample-form">
            <h4>Sample Form:</h4>
            <form method="get">
                <label for="name">Enter your name:</label>
                <input type="text" id="name" name="name" value="{{ request.GET.name|default:'' }}" placeholder="Your name here">
                <button type="submit">Submit</button>
            </form>
            {% if request.GET.name %}
                <p class="greeting">Hello, {{ request.GET.name }}! Welcome to the inventory system.</p>
            {% endif %}
        </div>
    </div>
    
    <div class="navigation">
        <a href="{% url 'inventory:index' %}" class="btn">← Back to Home</a>
    </div>
</div>

<style>
    .hello-world-section {
        padding: 20px 0;
    }
    .info-box {
        background-color: #e9f4ff;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border-left: 4px solid #007bff;
    }
    .info-box ul {
        list-style-type: none;
        padding: 0;
    }
    .info-box li {
        margin: 8px 0;
    }
    .demo-content {
        margin: 20px 0;
    }
    .sample-form {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
    }
    .sample-form input {
        padding: 8px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }
    .sample-form button {
        padding: 8px 16px;
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }
    .sample-form button:hover {
        background-color: #218838;
    }
    .greeting {
        color: #28a745;
        font-weight: bold;
        margin-top: 10px;
    }
    .btn {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 4px;
        margin-top: 20px;
    }
    .btn:hover {
        background-color: #0056b3;
    }
</style>

<script>
    // Display current timestamp
    document.getElementById('timestamp').textContent = new Date().toLocaleString();
</script>
{% endblock %}
