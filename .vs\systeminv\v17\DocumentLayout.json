{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\systeminv\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\forms.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\forms.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\systeminv\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:systeminv\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\templates\\inventory\\hello_world.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\templates\\inventory\\hello_world.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\tests.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\tests.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\apps.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\apps.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\inventory\\admin.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory\\admin.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\systeminv\\systeminv\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:systeminv\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 10, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1a46fd64-28d5-0019-8eb3-17a02d419b53}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "hello_world.html", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\templates\\inventory\\hello_world.html", "RelativeDocumentMoniker": "inventory\\templates\\inventory\\hello_world.html", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\templates\\inventory\\hello_world.html", "RelativeToolTip": "inventory\\templates\\inventory\\hello_world.html", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-08-11T03:48:59.478Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "tests.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\tests.py", "RelativeDocumentMoniker": "inventory\\tests.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\tests.py", "RelativeToolTip": "inventory\\tests.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:40:08.009Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "apps.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\apps.py", "RelativeDocumentMoniker": "inventory\\apps.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\apps.py", "RelativeToolTip": "inventory\\apps.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:39:33.405Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "admin.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\admin.py", "RelativeDocumentMoniker": "inventory\\admin.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\admin.py", "RelativeToolTip": "inventory\\admin.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:39:15.763Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "views.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\views.py", "RelativeDocumentMoniker": "inventory\\views.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\views.py", "RelativeToolTip": "inventory\\views.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:30:15.675Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "models.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\models.py", "RelativeDocumentMoniker": "inventory\\models.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\models.py", "RelativeToolTip": "inventory\\models.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:29:47.11Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "forms.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\forms.py", "RelativeDocumentMoniker": "inventory\\forms.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\forms.py", "RelativeToolTip": "inventory\\forms.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:28:50.223Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "urls.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\inventory\\urls.py", "RelativeDocumentMoniker": "inventory\\urls.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\inventory\\urls.py", "RelativeToolTip": "inventory\\urls.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:21:28.434Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "urls.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\systeminv\\urls.py", "RelativeDocumentMoniker": "systeminv\\urls.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\systeminv\\urls.py", "RelativeToolTip": "systeminv\\urls.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:13:34.688Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "settings.py", "DocumentMoniker": "C:\\Users\\<USER>\\systeminv\\systeminv\\settings.py", "RelativeDocumentMoniker": "systeminv\\settings.py", "ToolTip": "C:\\Users\\<USER>\\systeminv\\systeminv\\settings.py", "RelativeToolTip": "systeminv\\settings.py", "ViewState": "AgIAABwAAAAAAAAAAAAUwCcAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-08-11T03:02:17.871Z", "EditorCaption": ""}]}]}]}